import { pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";

// Example of defining a schema in Drizzle ORM:
export const todoTable = pgTable("todos", {
  id: uuid().primaryKey().defaultRandom(),
  text: varchar({ length: 255 }).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

// You can then infer the types for selecting and inserting
export type TodoItem = typeof todoTable.$inferSelect;
export type TodoInsert = typeof todoTable.$inferInsert;
