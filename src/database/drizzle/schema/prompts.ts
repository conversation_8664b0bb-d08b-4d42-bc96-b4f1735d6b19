import { prompts } from "@/lib/prompts";
import { integer, pgTable, text, timestamp } from "drizzle-orm/pg-core";

// Example of defining a schema in Drizzle ORM:
export const promptTable = pgTable("prompts", {
  id: text().$type<keyof typeof prompts>().primary<PERSON>ey(),
  model: text().notNull(),
  prompt: text().notNull(),
  maxTokens: integer().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

// You can then infer the types for selecting and inserting
export type PromptItem = typeof promptTable.$inferSelect;
export type PromptInsert = typeof promptTable.$inferInsert;
