import { relations } from "drizzle-orm";
import { jsonb, pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { user } from "./auth";

// Example of defining a schema in Drizzle ORM:
export const jobTable = pgTable("jobs", {
  id: uuid().primaryKey().defaultRandom(),
  name: text().notNull(),
  data: jsonb().notNull(),
  addedAt: timestamp(),
  endedAt: timestamp(),
  status: text({ enum: ["pending", "completed"] }),
  userId: text().references(() => user.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export const jobRelations = relations(jobTable, ({ one }) => ({
  user: one(user, { fields: [jobTable.userId], references: [user.id] }),
}));

// You can then infer the types for selecting and inserting
export type JobItem = typeof jobTable.$inferSelect;
export type JobInsert = typeof jobTable.$inferInsert;
