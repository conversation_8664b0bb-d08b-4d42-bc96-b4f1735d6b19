import * as packageQueries from "@/database/drizzle/queries/packages";
import * as subscriptionQueries from "@/database/drizzle/queries/subscriptions";
import {
  cancelStripeSubscription,
  createBillingPortalSession,
  createStripeCustomer,
  createStripeSubscription,
  getStripeSubscription,
} from "./stripe";
import { getUser } from "@/database/drizzle/queries/users";

export interface CreateSubscriptionParams {
  userId: string;
  packageId: string;
}

export interface CreateSubscriptionResult {
  subscriptionId: string;
  clientSecret: string;
  customerId: string;
}

export async function createSubscription(db: DB, params: CreateSubscriptionParams): Promise<CreateSubscriptionResult> {
  const { userId, packageId } = params;

  // Get the package details
  const [packageData] = await packageQueries.getPackageById(db, packageId);
  if (!packageData) {
    throw new Error("Package not found");
  }

  if (!packageData.stripePriceId) {
    throw new Error("Package does not have a Stripe price ID");
  }

  // Check if user already has an active subscription
  const existingSubscription = await subscriptionQueries.getActiveUserSubscription(db, userId);
  if (existingSubscription) {
    throw new Error("User already has an active subscription");
  }

  // Create or get Stripe customer
  const stripeCustomer = await createOrGetStripeCustomer(db, userId);

  // Create Stripe subscription
  const stripeSubscription = await createStripeSubscription(stripeCustomer.id, packageData.stripePriceId, {
    userId: userId,
    packageId: packageId.toString(),
  });

  // Save subscription to database
  await subscriptionQueries.insertSubscription(db, {
    userId,
    packageId,
    stripeSubscriptionId: stripeSubscription.id,
    stripeCustomerId: stripeCustomer.id,
    status: stripeSubscription.status as any,
    currentPeriodStart: new Date(stripeSubscription.items.data[0].current_period_start * 1000),
    currentPeriodEnd: new Date(stripeSubscription.items.data[0].current_period_end * 1000),
    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
    trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
    trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
  });

  // Extract client secret from the payment intent
  const clientSecret = (stripeSubscription.latest_invoice as any)?.payment_intent?.client_secret;

  if (!clientSecret) {
    throw new Error("Failed to create payment intent");
  }

  return {
    subscriptionId: stripeSubscription.id,
    clientSecret,
    customerId: stripeCustomer.id,
  };
}

export async function createOrGetStripeCustomer(db: DB, userId: string) {
  const user = await getUser(db, userId);
  let stripeCustomer;
  const existingUserSubscription = await subscriptionQueries.getUserSubscription(db, userId);

  if (existingUserSubscription?.stripeCustomerId) {
    // Use existing customer
    stripeCustomer = { id: existingUserSubscription.stripeCustomerId };
  } else {
    // Create new customer
    stripeCustomer = await createStripeCustomer(user.email, user.name, { userId });
  }
  return stripeCustomer;
}

export async function cancelUserSubscription(db: DB, userId: string, cancelAtPeriodEnd: boolean = true) {
  const subscription = await subscriptionQueries.getActiveUserSubscription(db, userId);

  if (!subscription) {
    throw new Error("No active subscription found");
  }

  if (!subscription.stripeSubscriptionId) {
    throw new Error("Subscription does not have a Stripe subscription ID");
  }

  // Cancel in Stripe
  await cancelStripeSubscription(subscription.stripeSubscriptionId, cancelAtPeriodEnd);

  // Update in database
  await subscriptionQueries.updateSubscription(db, subscription.id, {
    cancelAtPeriodEnd,
    ...(cancelAtPeriodEnd ? {} : { status: "canceled", canceledAt: new Date() }),
  });

  return subscription;
}

export async function createBillingPortal(db: DB, userId: string, returnUrl: string) {
  const subscription = await subscriptionQueries.getUserSubscription(db, userId);

  if (!subscription?.stripeCustomerId) {
    throw new Error("No customer found for user");
  }

  return await createBillingPortalSession(subscription.stripeCustomerId, returnUrl);
}

export async function syncSubscriptionWithStripe(db: DB, subscriptionId: string) {
  const subscription = await subscriptionQueries.getUserSubscription(db, ""); // We need to modify this query

  if (!subscription?.stripeSubscriptionId) {
    throw new Error("Subscription does not have a Stripe subscription ID");
  }

  const stripeSubscription = await getStripeSubscription(subscription.stripeSubscriptionId);

  // Update database with latest Stripe data
  await subscriptionQueries.updateSubscription(db, subscriptionId, {
    status: stripeSubscription.status as any,
    currentPeriodStart: new Date(stripeSubscription.items.data[0].current_period_start * 1000),
    currentPeriodEnd: new Date(stripeSubscription.items.data[0].current_period_end * 1000),
    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
    canceledAt: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
    trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
    trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
  });

  return stripeSubscription;
}

export async function getUserSubscriptionStatus(db: DB, userId: string) {
  const subscription = await subscriptionQueries.getActiveUserSubscription(db, userId);

  if (!subscription) {
    return { hasActiveSubscription: false, subscription: null };
  }

  const isActive = subscription.status === "active" || subscription.status === "trialing";
  const isInTrial = subscription.status === "trialing";
  const willCancelAtPeriodEnd = subscription.cancelAtPeriodEnd;

  return {
    hasActiveSubscription: isActive,
    subscription,
    isInTrial,
    willCancelAtPeriodEnd,
    currentPeriodEnd: subscription.currentPeriodEnd,
  };
}
