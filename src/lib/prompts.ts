import { sectionSchema } from "@/database/drizzle/schema/videos";
import Handlebars from "handlebars";
import z from "zod";

export function getAIOptions<T extends keyof typeof prompts>(
  name: T,
  params: Record<keyof (typeof prompts)[T]["paramDescriptions"], string | number>,
) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { paramDescriptions, ...defaultOptions } = prompts[name];

  return {
    ...defaultOptions,
    prompt: Handlebars.compile(prompts[name].prompt)(params),
  };
}

const transcriptFromAISchema = z.object({ sections: z.array(sectionSchema.omit({ imageUrl: true })) });

export const prompts = {
  TargetSuggestion: {
    model: "sonar-pro",
    prompt: `Create a script for {{theme}} in short video format for {{target}}.
      Follow the following instructions, respond with JSON in the provided response format:
      Aim for exactly {{wordCount}} words to match {{durationMinutes}} minutes of narration at 150 WPM. Short sentences. Natural voiceover style.`,
    maxTokens: 10000,
    schema: transcriptFromAISchema,
    paramDescriptions: {
      theme: "The theme or subject of the video (e.g., productivity tips, fitness, history fact).",
      target: "The intended audience for the video (e.g., students, professionals, beginners).",
      wordCount: "The exact number of words in the script, based on narration speed.",
      durationMinutes: "The target duration of the video in minutes.",
    },
  },
  VideoTitles: {
    model: "sonar",
    prompt: `Give exactly {{noOfVideos}} titles for videos 
      of type {{theme}} in short video format for {{target}}.`,
    maxTokens: 10000,
    schema: z.object({ titles: z.array(z.string()) }),
    paramDescriptions: {
      noOfVideos: "The exact number of video titles to generate.",
      theme: "The type or subject of the videos (e.g., cooking, finance, travel).",
      target: "The intended audience for the videos (e.g., kids, developers, entrepreneurs).",
    },
  },
  VideoTranscript: {
    model: "sonar-pro",
    prompt: `Create a script for {{theme}} in short video format for {{target}}.
      Follow the following instructions, respond with JSON in the provided response format:
      Aim for exactly {{wordCount}} words to match {{durationMinutes}} minutes of narration at 150 WPM. Short sentences. Natural voiceover style.`,
    maxTokens: 10000,
    schema: transcriptFromAISchema,
    paramDescriptions: {
      theme: "The theme or subject of the video (e.g., motivation, tech review, fun fact).",
      target: "The intended audience for the video (e.g., teenagers, gamers, teachers).",
      wordCount: "The exact number of words in the script, matching narration length.",
      durationMinutes: "The target duration of the video in minutes.",
    },
  },
};
