import Stripe from "stripe";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is required");
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-08-27.basil",
  typescript: true,
});

export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

// Stripe webhook event types we handle
export const STRIPE_WEBHOOK_EVENTS = {
  CUSTOMER_SUBSCRIPTION_CREATED: "customer.subscription.created",
  CUSTOMER_SUBSCRIPTION_UPDATED: "customer.subscription.updated",
  CUSTOMER_SUBSCRIPTION_DELETED: "customer.subscription.deleted",
  INVOICE_PAYMENT_SUCCEEDED: "invoice.payment_succeeded",
  INVOICE_PAYMENT_FAILED: "invoice.payment_failed",
  CUSTOMER_CREATED: "customer.created",
  CUSTOMER_UPDATED: "customer.updated",
} as const;

export type StripeWebhookEvent = keyof typeof STRIPE_WEBHOOK_EVENTS;

// Helper function to construct webhook event
export function constructWebhookEvent(body: string | Buffer, signature: string) {
  if (!STRIPE_WEBHOOK_SECRET) {
    throw new Error("STRIPE_WEBHOOK_SECRET is required");
  }

  return stripe.webhooks.constructEvent(body, signature, STRIPE_WEBHOOK_SECRET);
}

// Helper function to create a customer
export async function createStripeCustomer(email: string, name?: string, metadata?: Record<string, string>) {
  return await stripe.customers.create({
    email,
    name,
    metadata,
  });
}

// Helper function to create a subscription
export async function createStripeSubscription(customerId: string, priceId: string, metadata?: Record<string, string>) {
  return await stripe.subscriptions.create({
    customer: customerId,
    items: [{ price: priceId }],
    payment_behavior: "default_incomplete",
    payment_settings: { save_default_payment_method: "on_subscription" },
    expand: ["latest_invoice.payment_intent"],
    metadata,
  });
}

// Helper function to cancel a subscription
export async function cancelStripeSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true) {
  if (cancelAtPeriodEnd) {
    return await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });
  } else {
    return await stripe.subscriptions.cancel(subscriptionId);
  }
}

// Helper function to update a subscription
export async function updateStripeSubscription(subscriptionId: string, updates: Stripe.SubscriptionUpdateParams) {
  return await stripe.subscriptions.update(subscriptionId, updates);
}

// Helper function to retrieve a subscription
export async function getStripeSubscription(subscriptionId: string) {
  return await stripe.subscriptions.retrieve(subscriptionId);
}

// Helper function to create a billing portal session
export async function createBillingPortalSession(customerId: string, returnUrl: string) {
  return await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  });
}

// Helper function to create a Stripe product
export async function createStripeProduct(name: string, description?: string, metadata?: Record<string, string>) {
  return await stripe.products.create({
    name,
    description,
    metadata,
  });
}

// Helper function to create a Stripe price
export async function createStripePrice(
  productId: string,
  unitAmount: number,
  currency: string = "usd",
  interval: "month" | "year" = "month",
  metadata?: Record<string, string>
) {
  return await stripe.prices.create({
    product: productId,
    unit_amount: unitAmount, // Amount in cents
    currency,
    recurring: {
      interval,
    },
    metadata,
  });
}
