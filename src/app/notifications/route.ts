import { NextRequest } from "next/server";
import { getJob } from "@/database/drizzle/queries/jobs";
import { QGenerateVideo } from "@/jobs/queues";
import { QueueEvents } from "bullmq";

import { getDataContext } from "@/lib/getDataContext";
import { redisClient } from "@/lib/redis";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function GET(req: NextRequest) {
  const responseStream = new TransformStream();
  const writer = responseStream.writable.getWriter();
  const encoder = new TextEncoder();

  async function writeSSE({ data, event, id }: { data: string; event?: string; id?: string }) {
    let payload = "";
    if (id) payload += `id: ${id}\n`;
    if (event) payload += `event: ${event}\n`;

    // SSE requires each data line to start with "data:"
    const dataLines = data.split("\n").map((line) => `data: ${line}`);
    payload += dataLines.join("\n") + "\n\n";

    await writer.write(encoder.encode(payload));
  }

  try {
    const { session, db } = await getDataContext();
    if (!session?.user) {
      await writer.close();
      return Response.json({ error: "Not authenticated" }, { status: 401 });
    }

    const generateVideoEvents = new QueueEvents(QGenerateVideo.name, { connection: redisClient });

    generateVideoEvents.on("active", async ({ jobId }) => {
      const [job] = await getJob(db, jobId);
      if (job.userId === session.user.id) {
        await writeSSE({
          data: JSON.stringify({ status: "completed", job }),
          id: String(Date.now()),
        });
      }
    });

    generateVideoEvents.on("completed", async ({ jobId }) => {
      const [job] = await getJob(db, jobId);
      if (job.userId === session.user.id) {
        await writeSSE({
          data: JSON.stringify({ status: "completed", job }),
          id: String(Date.now()),
        });
      }
    });

    generateVideoEvents.on("failed", () => {
      // Called whenever a job is moved to failed by any worker.
    });

    generateVideoEvents.on("progress", async (event) => {
      const [job] = await getJob(db, event.jobId);
      if (job.userId === session.user.id) {
        await writeSSE({
          data: JSON.stringify({ status: "progress", job, data: event.data }),
          id: String(Date.now()),
        });
      }
    });

    const heartbeat = setInterval(async () => {
      await writeSSE({
        data: "Lub-dub",
        event: "heartbeat",
        id: String(Date.now()),
      });
    }, 30000);

    req.signal.addEventListener("abort", () => {
      clearInterval(heartbeat);
      generateVideoEvents.removeAllListeners();
      writer.close();
    });
  } catch (error) {
    console.log(error);
    await writer.close();
  }

  return new Response(responseStream.readable, {
    headers: {
      "Content-Type": "text/event-stream",
      Connection: "keep-alive",
      "Cache-Control": "no-cache, no-transform",
    },
  });
}
