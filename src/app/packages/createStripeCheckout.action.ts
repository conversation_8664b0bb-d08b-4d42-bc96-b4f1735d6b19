"use server";

import * as packageQueries from "@/database/drizzle/queries/packages";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

import { stripe, createStripeProduct, createStripePrice } from "@/lib/stripe";

interface CreateStripeCheckoutParams {
  packageId: string;
  userId: string;
  userEmail: string;
  userName?: string;
}

export async function onCreateStripeCheckout(params: CreateStripeCheckoutParams) {
  const { db } = await getDataContext();
  const { packageId, userId, userEmail } = params;

  // Get the package details
  const [packageData] = await packageQueries.getPackageById(db, packageId);
  if (!packageData) {
    return { error: errors.PackageNotFound };
  }

  // If stripePriceId is missing, create it
  if (!packageData.stripePriceId) {
    try {
      console.log(`Creating Stripe product and price for package: ${packageData.name}`);

      // Create or get Stripe product
      let stripeProduct;
      if (packageData.stripeProductId) {
        // Try to retrieve existing product
        try {
          stripeProduct = await stripe.products.retrieve(packageData.stripeProductId);
        } catch (error) {
          console.warn(`Failed to retrieve existing product ${packageData.stripeProductId}, creating new one:`, error);
          stripeProduct = null;
        }
      }

      if (!stripeProduct) {
        // Create new Stripe product
        stripeProduct = await createStripeProduct(
          `AI Shorts ${packageData.name}`,
          packageData.description || undefined,
          {
            packageId: packageData.id,
            billingInterval: packageData.billingInterval,
          }
        );
      }

      // Create Stripe price
      const unitAmount = Math.round(parseFloat(packageData.price) * 100); // Convert to cents
      const stripePrice = await createStripePrice(
        stripeProduct.id,
        unitAmount,
        packageData.currency,
        packageData.billingInterval as "month" | "year",
        {
          packageId: packageData.id,
          packageName: packageData.name,
        }
      );

      // Update package with new Stripe IDs
      const [updatedPackage] = await packageQueries.updatePackage(db, packageData.id, {
        stripeProductId: stripeProduct.id,
        stripePriceId: stripePrice.id,
      });

      if (!updatedPackage) {
        console.error("Failed to update package with Stripe IDs");
        return { error: errors.InternalServerError };
      }

      // Update packageData with new stripePriceId for the checkout session
      packageData.stripePriceId = stripePrice.id;
      packageData.stripeProductId = stripeProduct.id;

      console.log(`Successfully created Stripe product ${stripeProduct.id} and price ${stripePrice.id} for package ${packageData.name}`);
    } catch (error) {
      console.error("Failed to create Stripe product/price:", error);
      return { error: errors.StripeCheckoutFailed };
    }
  }

  // For free packages, we don't need Stripe checkout
  if (parseFloat(packageData.price) === 0) {
    return { error: errors.FreePackageNoCheckout };
  }

  try {
    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price: packageData.stripePriceId,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${process.env.FRONTEND_URL || "http://localhost:5002"}/packages?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL || "http://localhost:5002"}/packages?canceled=true`,
      customer_email: userEmail,
      metadata: {
        userId: userId,
        packageId: packageId.toString(),
      },
      subscription_data: {
        metadata: {
          userId: userId,
          packageId: packageId.toString(),
        },
      },
    });

    return {
      checkoutUrl: session.url,
      sessionId: session.id,
    };
  } catch (error) {
    console.error("Failed to create Stripe checkout session:", error);
    return { error: errors.StripeCheckoutFailed };
  }
}
