"use server";

import { VideoIdeaInsert, VideoInsert } from "@/database/drizzle/schema/videos";
import { addJob, QGenerateVideo } from "@/jobs/queues";
import { ai } from "@/lib/ai";
import { getVoices } from "@/lib/elevenlabs";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import z from "zod";
import * as drizzleQueries from "../../database/drizzle/queries/videos";

export async function onNewVideo(data: VideoInsert, delay = 0) {
  const context = await getDataContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  data.voiceId ||= "tnSpp4vdxKPjI9w0GnoV";
  data.userId = context.session.user.id;
  const record = (await drizzleQueries.insertVideo(context.db, data).returning())[0];

  await addJob(context.db, context.session.user.id, QGenerateVideo, { videoId: record.id }, delay);

  return record;
}

export async function onGetTargetSuggestion(theme: string) {
  const { output } = await ai.getStructuredOutput({
    model: "sonar",
    prompt: `Suggest 3 completions for the following prompt:
       Create a script for ${theme} in short video format for <rest of prompt>.
       Only give the <rest of prompt> part in suggestions, try to be less specific.
       `,
    maxTokens: 10000,
    schema: z.object({ suggestions: z.array(z.string()) }),
  });

  return output;
}

export async function onGetVideoTitles(data: VideoIdeaInsert) {
  const { output } = await ai.getStructuredOutput({
    model: "sonar",
    prompt: `Give exactly ${data.days * data.frequency} titles for videos 
       of type ${data.theme} in short video format for ${data.target}.
       `,
    maxTokens: 10000,
    schema: z.object({ titles: z.array(z.string()) }),
  });

  return output;
}

export async function onSaveVideoIdea(data: VideoIdeaInsert) {
  const context = await getDataContext();
  const videoIdea = (await drizzleQueries.insertVideoIdea(context.db, data).returning())[0];
  const interval = (24 * 60 * 60 * 1000) / data.frequency;

  await Promise.all(
    data.videos?.map(async (x, i) => {
      await onNewVideo({ ...videoIdea, target: x.title }, i * interval);
    }) || [],
  );
}

export async function onGetMoreVoices(...args: Parameters<typeof getVoices>) {
  return await getVoices(...args);
}
